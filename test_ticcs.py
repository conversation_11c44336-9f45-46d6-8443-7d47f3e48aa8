#!/usr/bin/env python3
"""
测试Ti CCS map文件处理功能
"""

import sys
import os
sys.path.append('src')

from map_handle_ticcs import TiCcsMapFileParser
from main import detect_map_file_type, create_map_parser

def test_ticcs_detection():
    """测试Ti CCS map文件类型检测"""
    print("测试Ti CCS map文件类型检测...")
    
    # 测试Ti CCS文件
    ticcs_file = "int_rpu_arm7r5_mcu10_ticcs.elf.map"
    if os.path.exists(ticcs_file):
        map_type = detect_map_file_type(ticcs_file)
        print(f"  {ticcs_file}: {map_type}")
        assert map_type == 'ticcs', f"期望 'ticcs'，实际得到 '{map_type}'"
    else:
        print(f"  警告: {ticcs_file} 不存在")
    
    # 测试ARM GCC文件
    gcc_file = "int_rpu_arm8r5_arm_gcc.elf.map"
    if os.path.exists(gcc_file):
        map_type = detect_map_file_type(gcc_file)
        print(f"  {gcc_file}: {map_type}")
        assert map_type == 'arm_gcc', f"期望 'arm_gcc'，实际得到 '{map_type}'"
    else:
        print(f"  警告: {gcc_file} 不存在")
    
    print("✅ 文件类型检测测试通过")

def test_ticcs_parsing():
    """测试Ti CCS map文件解析"""
    print("\n测试Ti CCS map文件解析...")
    
    ticcs_file = "int_rpu_arm7r5_mcu10_ticcs.elf.map"
    if not os.path.exists(ticcs_file):
        print(f"  警告: {ticcs_file} 不存在，跳过解析测试")
        return
    
    try:
        parser = TiCcsMapFileParser(ticcs_file)
        parser.parse_map_file()
        
        print(f"  内存配置区域: {len(parser.memory_config)} 个")
        print(f"  段数量: {len(parser.sections)} 个")
        print(f"  符号数量: {len(parser.symbols)} 个")
        print(f"  模块数量: {len(parser.module_usage)} 个")
        
        # 测试内存使用信息
        text, data, bss, other = parser.get_memory_usage()
        print(f"  Text: {text:,} bytes ({text/1024:.1f} KB)")
        print(f"  Data: {data:,} bytes ({data/1024:.1f} KB)")
        print(f"  BSS:  {bss:,} bytes ({bss/1024:.1f} KB)")
        print(f"  Total: {text+data+bss:,} bytes ({(text+data+bss)/1024:.1f} KB)")
        
        # 测试符号搜索
        results = parser.search_symbol('main')
        print(f"  符号搜索 'main': 找到 {len(results)} 个结果")
        
        # 测试地址搜索
        if parser.symbols:
            first_symbol = next(iter(parser.symbols.values()))
            addr = f"{first_symbol['address']:08x}"
            closest, next_sym = parser.search_address(addr)
            if closest:
                print(f"  地址搜索 0x{addr}: 找到符号 {closest[0]}")
        
        print("✅ Ti CCS map文件解析测试通过")
        
    except Exception as e:
        print(f"❌ Ti CCS map文件解析失败: {e}")
        raise

def test_unified_parser():
    """测试统一的解析器接口"""
    print("\n测试统一的解析器接口...")
    
    test_files = [
        ("int_rpu_arm7r5_mcu10_ticcs.elf.map", "ticcs"),
        ("int_rpu_arm8r5_arm_gcc.elf.map", "arm_gcc")
    ]
    
    for filename, expected_type in test_files:
        if not os.path.exists(filename):
            print(f"  警告: {filename} 不存在，跳过测试")
            continue
            
        try:
            parser = create_map_parser(filename)
            parser.parse_map_file()
            
            # 检查解析器类型
            actual_type = type(parser).__name__
            if expected_type == "ticcs":
                assert "TiCcs" in actual_type, f"期望Ti CCS解析器，实际得到 {actual_type}"
            else:
                assert "MapFileParser" in actual_type and "TiCcs" not in actual_type, f"期望ARM GCC解析器，实际得到 {actual_type}"
            
            # 测试基本功能
            text, data, bss, other = parser.get_memory_usage()
            total = text + data + bss + other
            
            print(f"  {filename}: {actual_type}")
            print(f"    内存总计: {total:,} bytes ({total/1024:.1f} KB)")
            print(f"    段数量: {len(parser.sections)}")
            print(f"    符号数量: {len(parser.symbols)}")
            
        except Exception as e:
            print(f"❌ {filename} 解析失败: {e}")
            raise
    
    print("✅ 统一解析器接口测试通过")

def main():
    """主测试函数"""
    print("Ti CCS Map文件处理功能测试")
    print("=" * 50)
    
    try:
        test_ticcs_detection()
        test_ticcs_parsing()
        test_unified_parser()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！Ti CCS map文件处理功能正常工作。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
