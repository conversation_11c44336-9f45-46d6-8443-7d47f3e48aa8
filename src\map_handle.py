import re
import os
from pathlib import Path


class MapFileParser:
    """MAP文件解析器"""
    
    def __init__(self, map_file_path):
        self.map_file_path = Path(map_file_path)
        self.sections = {}
        self.symbols = {}
        self.memory_config = {}
        self.memory_usage = {}
        self.module_usage = {}
        self.text_size = 0
        self.data_size = 0
        self.bss_size = 0
        
    def parse_map_file(self):
        """解析MAP文件"""
        if not self.map_file_path.exists():
            raise FileNotFoundError(f"MAP文件不存在: {self.map_file_path}")
            
        with open(self.map_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        self._parse_memory_configuration(content)
        self._parse_sections(content)
        self._parse_symbols(content)
        self._calculate_sizes()
        self._calculate_memory_usage(content)
        self._calculate_module_usage(content)
        
    def _parse_memory_configuration(self, content):
        """解析Memory Configuration部分"""
        # 查找Memory Configuration部分
        mem_config_match = re.search(r'Memory Configuration\s*\n\s*\n\s*Name\s+Origin\s+Length\s+Attributes\s*\n(.*?)\n\s*\n', 
                                    content, re.DOTALL)
        if not mem_config_match:
            return
            
        mem_config_text = mem_config_match.group(1)
        
        # 解析每个memory段
        # 格式: Name Origin Length [Attributes]
        mem_pattern = r'^([^\s]+)\s+0x([0-9a-fA-F]+)\s+0x([0-9a-fA-F]+)(?:\s+(.*))?$'
        
        for line in mem_config_text.split('\n'):
            line = line.strip()
            if not line or line.startswith('*default*'):
                continue
                
            match = re.match(mem_pattern, line)
            if match:
                name = match.group(1)
                origin = int(match.group(2), 16)
                length = int(match.group(3), 16)
                attributes = match.group(4) if match.group(4) else ""
                
                self.memory_config[name] = {
                    'origin': origin,
                    'length': length,
                    'attributes': attributes,
                    'end': origin + length
                }
                
    def _calculate_memory_usage(self, content):
        """计算每个memory段的使用情况"""
        if not self.memory_config:
            return
            
        # 初始化使用情况
        for mem_name in self.memory_config:
            self.memory_usage[mem_name] = {
                'used_size': 0,
                'sections': []
            }
        
        # 分析段的分配情况，计算每个memory段的使用
        for section_name, section_info in self.sections.items():
            section_addr = section_info['address']
            section_size = section_info['size']
            
            if section_size == 0:
                continue
                
            # 找到这个段属于哪个memory区域
            for mem_name, mem_info in self.memory_config.items():
                if (section_addr >= mem_info['origin'] and 
                    section_addr < mem_info['end']):
                    self.memory_usage[mem_name]['used_size'] += section_size
                    self.memory_usage[mem_name]['sections'].append({
                        'name': section_name,
                        'address': section_addr,
                        'size': section_size
                    })
                    break
        
    def get_memory_usage_info(self):
        """获取Memory Configuration使用信息"""
        if not self.memory_config:
            return "未找到Memory Configuration信息"
            
        info_lines = []
        info_lines.append("")
        
        # 表头
        header = f"{'Name':<20} {'Origin':<12} {'Length':<12} {'Used':<12} {'Usage %':<8} {'Main Sections'}"
        info_lines.append(header)
        info_lines.append("-" * 80)
        
        # 按起始地址排序
        sorted_memories = sorted(self.memory_config.items(), key=lambda x: x[1]['origin'])
        
        for mem_name, mem_info in sorted_memories:
            used_size = self.memory_usage.get(mem_name, {}).get('used_size', 0)
            usage_percent = (used_size / mem_info['length'] * 100) if mem_info['length'] > 0 else 0
            
            # 获取主要使用的段（最多3个）
            main_sections = ""
            if used_size > 0 and mem_name in self.memory_usage:
                sections = self.memory_usage[mem_name]['sections']
                if sections:
                    # 按大小排序，取前3个
                    sorted_sections = sorted(sections, key=lambda x: x['size'], reverse=True)
                    main_section_names = [s['name'] for s in sorted_sections[:3]]
                    main_sections = ', '.join(main_section_names)
            
            line = (f"{mem_name:<20} "
                   f"0x{mem_info['origin']:08x}   "
                   f"0x{mem_info['length']:08x}   "
                   f"0x{used_size:08x}   "
                   f"{usage_percent:6.1f}%   "
                   f"{main_sections}")
            info_lines.append(line)
        
        return '\n'.join(info_lines)
        
    def _calculate_module_usage(self, content):
        """计算各个模块的内存使用情况"""
        # 在Linker script and memory map之后查找所有段和对应的目标文件
        linker_script_match = re.search(r'Linker script and memory map', content)
        if not linker_script_match:
            return
            
        linker_content = content[linker_script_match.end():]
        
        # 匹配子段和对应的文件信息
        # 格式: .section 0xaddress 0xsize file.a(file.o)
        # 注意开头有空格的是子段，但也要匹配主段内的内容
        # 修改正则表达式以匹配更多内容
        subsection_pattern = r'^\s+\.([\w\._]+)\s+0x([0-9a-f]+)\s+0x([0-9a-f]+)\s+(.+?)(?:\n|$)'
        matches = re.finditer(subsection_pattern, linker_content, re.MULTILINE)

        # 同时匹配主段内的函数和变量定义
        # 格式: 0xaddress symbol_name
        symbol_pattern = r'^\s+0x([0-9a-f]+)\s+([a-zA-Z_][^\s\n]+)\s*$'
        symbol_matches = re.finditer(symbol_pattern, linker_content, re.MULTILINE)
        
        for match in matches:
            section_name = match.group(1)
            address = int(match.group(2), 16)
            size = int(match.group(3), 16)
            file_info = match.group(4).strip()
            
            if size == 0:
                continue
                
            # 解析模块名称
            module_name = self._extract_module_name(file_info)
            if not module_name:
                continue
                
            # 根据段名确定类型 - 优化版本
            section_type = self._classify_section_type(section_name)
            
            # 跳过不需要统计的段
            if section_type == 'skip':
                continue
                
            # 统计模块使用情况 - 移除other分类
            if module_name not in self.module_usage:
                self.module_usage[module_name] = {
                    'text': 0, 'data': 0, 'bss': 0, 'total': 0
                }

            # 由于改进了分类器，现在所有段都应该被归类到text/data/bss中
            # 如果分类器返回了其他值，统一归类到data
            if section_type not in ['text', 'data', 'bss']:
                section_type = 'data'

            self.module_usage[module_name][section_type] += size
            self.module_usage[module_name]['total'] += size
    
    def _extract_module_name(self, file_info):
        """从文件信息中提取模块名称"""
        # 移除路径和额外信息，只保留文件名
        file_info = file_info.split()[0] if file_info else ""
        
        # 处理特殊的模块名格式，如 *libJ6_MCU_MCAL.a:Ipc_*.o(.bss)
        # 提取其中的主库名
        special_lib_match = re.match(r'\*([^:]+\.(?:a|lib))', file_info)
        if special_lib_match:
            lib_path = special_lib_match.group(1)
            lib_name = lib_path.split('/')[-1]
            return lib_name
        
        # 匹配lib.a(file.o)格式
        lib_match = re.match(r'(.+\.(?:a|lib))\(', file_info)
        if lib_match:
            lib_path = lib_match.group(1)
            # 提取库文件名（不包含路径）
            lib_name = lib_path.split('/')[-1]
            return lib_name
            
        # 匹配单独的.o或其他文件
        if '.o' in file_info or '.obj' in file_info:
            # 提取路径中的模块信息
            if '/' in file_info:
                path_parts = file_info.split('/')
                # 寻找可能的模块名称
                for part in path_parts:
                    if 'lib' in part and ('.a' in part or '.lib' in part):
                        return part
                # 如果没有找到库文件，使用目录名
                for part in reversed(path_parts[:-1]):  # 不包含文件名
                    if part and part != 'Release' and part != 'Debug':
                        return f"{part}.o"
            return file_info.split('/')[-1]  # 返回文件名
            
        return None
        
    def _classify_section_type(self, section_name):
        """
        更准确的段类型分类器 - 基于ELF标准和arm-none-eabi-size的分类逻辑
        参考ELF段属性：AX=text, WA+PROGBITS=data, WA+NOBITS=bss
        优化版本：尽量避免OTHER分类，将段归入标准三大类
        特别优化了text段的识别，解决text段被误分类为data的问题
        """
        section_lower = section_name.lower()

        # 需要跳过的段（元数据、调试信息等）
        skip_patterns = [
            'debug_', 'comment', 'note', 'gnu.', 'symtab',
            'strtab', 'shstrtab', 'rel.', 'rela.', 'hash', 'dynsym',
            'dynstr', 'dynamic', 'interp', 'eh_frame', 'plt', 'got',
            'group', 'arm.attributes', 'stab'  # 添加stab调试段
        ]

        for pattern in skip_patterns:
            if pattern in section_lower:
                return 'skip'

        # TEXT段（可执行代码段）- 基于ELF的AX属性段
        # 这些段在ELF中具有ALLOC+EXEC属性
        text_sections = [
            '.startup_text', '.text', '.init', '.fini'
        ]

        # 检查精确匹配的text段
        if section_name in text_sections:
            return 'text'

        # 检查.text.开头的子段（这是最常见的text段格式）
        if section_name.startswith('.text.'):
            return 'text'

        # 优化的代码段识别 - 扩展对包含'text'的段的识别
        # 只排除明确知道是数据段的特殊情况
        if 'text' in section_lower:
            # 特殊情况：.mcal_text 确实是数据段，需要排除
            if section_name == '.mcal_text':
                pass  # 继续后续判断，不直接返回text
            else:
                return 'text'

        # 检查含有code的段（通常是可执行代码）
        code_patterns = ['_code', '.code', 'code_', 'code.']
        for pattern in code_patterns:
            if pattern in section_lower:
                return 'text'

        # BSS段（未初始化数据段）- 基于ELF的NOBITS类型
        # 这些段在ELF中具有ALLOC+WRITE属性但类型为NOBITS
        # 包括所有STACK段，因为它们是运行时分配的未初始化内存
        bss_patterns = [
            'bss', 'stack', 'heap', 'noinit', 'zero_init', 'common'
        ]

        for pattern in bss_patterns:
            if (pattern == section_lower or
                f'.{pattern}' == section_lower or
                section_lower.startswith(f'{pattern}.') or
                section_lower.startswith(f'.{pattern}.') or
                section_lower.endswith(f'_{pattern}') or
                section_lower.endswith(f'.{pattern}') or
                pattern in section_lower):  # 更宽松的匹配，包括各种STACK段
                return 'bss'

        # DATA段（已初始化数据段）- 基于ELF的PROGBITS类型且非可执行
        # 这些段在ELF中具有ALLOC属性且类型为PROGBITS，但不可执行
        # 包括u_boot_list段，因为它们是链接时生成的初始化数据
        data_patterns = [
            'data', 'rodata', 'init_array', 'fini_array', 'ctors', 'dtors',
            'const', 'mcal_text', 'shellcommand', 'u_boot_list',
            'mcu_shm_noncache', 'arm.exidx', 'var_init', 'var_', 'cfg_',
            'config', 'table', 'array', 'list'
        ]

        for pattern in data_patterns:
            if (pattern == section_lower or
                f'.{pattern}' == section_lower or
                section_lower.startswith(f'{pattern}.') or
                section_lower.startswith(f'.{pattern}.') or
                section_lower.endswith(f'_{pattern}') or
                section_lower.endswith(f'.{pattern}') or
                pattern in section_lower):  # 更宽松的匹配u_boot_list相关段
                return 'data'

        # 扩展的TEXT段识别 - 包含更多可执行代码段
        # 放在DATA段检查之后，避免与数据段关键词冲突
        extended_text_patterns = [
            'intvec', 'vector', 'startup', 'exception', 'interrupt',
            'os', 'kernel', 'rtos', 'isr', 'irq', 'handler',
            'func', 'function', 'boot', 'sys', 'task',
            'thread', 'proc', 'exec', 'run', 'scheduler'
        ]

        for pattern in extended_text_patterns:
            # 使用更精确的匹配，避免误匹配
            if (pattern == section_lower or
                f'.{pattern}' == section_lower or
                section_lower.startswith(f'{pattern}.') or
                section_lower.startswith(f'.{pattern}.') or
                section_lower.endswith(f'_{pattern}') or
                section_lower.endswith(f'.{pattern}') or
                f'_{pattern}_' in section_lower or
                f'.{pattern}.' in section_lower):
                return 'text'

        # 字符串常量段（通常归类为data）
        if '.str1' in section_lower or '.str2' in section_lower:
            return 'data'

        # 智能默认分类 - 根据段名特征进行推断
        # 优化版本：更积极地识别代码段
        if section_name.count('_') > 2 and any(c.isupper() for c in section_name):
            # 检查是否像代码段（包含CODE、CORE、FUNC等）
            code_keywords = ['CODE', 'FUNC', 'CORE', 'TASK', 'ISR', 'HANDLER', 'EXEC', 'RUN', 'PROC', 'THREAD']
            if any(keyword in section_name.upper() for keyword in code_keywords):
                return 'text'
            # 检查是否像变量段（包含VAR、DATA、CFG等）
            elif any(keyword in section_name.upper() for keyword in ['VAR', 'DATA', 'CFG', 'CONST', 'CONFIG']):
                return 'data'
            # 检查是否像BSS段（包含NOINIT、ZERO等）
            elif any(keyword in section_name.upper() for keyword in ['NOINIT', 'ZERO', 'UNINIT', 'BSS']):
                return 'bss'

        # 基于段名模式的进一步推断
        # 如果段名看起来像函数名或代码相关，倾向于归类为text
        if ('_' in section_name and
            any(pattern in section_lower for pattern in ['main', 'init', 'setup', 'loop', 'process', 'handle'])):
            return 'text'

        # 最后的默认分类：根据段的位置和大小特征
        # 如果实在无法分类，默认归为data（因为data是最常见的非代码段）
        return 'data'
        
    def get_module_usage_info(self):
        info_lines = []
        info_lines.append("")

        # 表头 - 移除Other列
        header = f"{'Module':<30} {'Text':<10} {'Data':<10} {'BSS':<10} {'Total':<10} {'%':<6}"
        info_lines.append(header)
        info_lines.append("-" * 80)

        # 初始化累加变量
        module_text_total = 0
        module_data_total = 0
        module_bss_total = 0
        module_grand_total = 0

        # 如果有模块统计数据，显示模块级别的分解
        if self.module_usage:
            # 按总大小排序
            sorted_modules = sorted(self.module_usage.items(), key=lambda x: x[1]['total'], reverse=True)

            # 先计算所有模块的总计，用于百分比计算
            for module_name, usage in sorted_modules:
                module_text_total += usage['text']
                module_data_total += usage['data']
                module_bss_total += usage['bss']
                module_grand_total += usage['total']

            # 显示各个模块
            for module_name, usage in sorted_modules:
                percent = (usage['total'] / module_grand_total * 100) if module_grand_total > 0 else 0

                # 移除Other列的显示
                line = (f"{module_name:<30} "
                       f"{usage['text']:<10} "
                       f"{usage['data']:<10} "
                       f"{usage['bss']:<10} "
                       f"{usage['total']:<10} "
                       f"{percent:5.1f}%")
                info_lines.append(line)
        else:
            # 如果没有模块统计，使用主段统计作为备用
            text_total, data_total, bss_total, other_total = self.get_memory_usage()
            module_text_total = text_total
            module_data_total = data_total
            module_bss_total = bss_total
            module_grand_total = text_total + data_total + bss_total + other_total
            info_lines.append(f"{'[Main Sections]':<30} {module_text_total:<10} {module_data_total:<10} {module_bss_total:<10} {module_grand_total:<10} {'100.0%':<6}")

        info_lines.append("")

        return '\n'.join(info_lines)

    def get_section_classification_debug(self):
        """获取段分类的调试信息，帮助诊断分类问题"""
        info_lines = []
        info_lines.append("")
        info_lines.append("段分类调试信息:")
        info_lines.append("")

        # 按类型分组显示段
        text_sections = []
        data_sections = []
        bss_sections = []
        other_sections = []
        skip_sections = []

        for section_name, section_info in self.sections.items():
            classified_type = self._classify_section_type(section_name)
            section_size = section_info['size']

            section_entry = f"{section_name} (0x{section_size:x} bytes)"

            if classified_type == 'text':
                text_sections.append(section_entry)
            elif classified_type == 'data':
                data_sections.append(section_entry)
            elif classified_type == 'bss':
                bss_sections.append(section_entry)
            elif classified_type == 'skip':
                skip_sections.append(section_entry)
            else:
                other_sections.append(section_entry)

        # 显示各类型的段
        if text_sections:
            info_lines.append("TEXT段:")
            for section in sorted(text_sections):
                info_lines.append(f"  {section}")
            info_lines.append("")

        if data_sections:
            info_lines.append("DATA段:")
            for section in sorted(data_sections):
                info_lines.append(f"  {section}")
            info_lines.append("")

        if bss_sections:
            info_lines.append("BSS段:")
            for section in sorted(bss_sections):
                info_lines.append(f"  {section}")
            info_lines.append("")

        if other_sections:
            info_lines.append("OTHER段:")
            for section in sorted(other_sections):
                info_lines.append(f"  {section}")
            info_lines.append("")

        if skip_sections:
            info_lines.append("跳过的段(调试信息等):")
            for section in sorted(skip_sections):
                info_lines.append(f"  {section}")
            info_lines.append("")

        return '\n'.join(info_lines)

    def _parse_sections(self, content):
        """解析节区信息"""
        # 查找主要的节区信息 (.text, .data, .bss等)
        # 匹配所有可能的段
        
        # 在Linker script and memory map之后查找段信息
        linker_script_match = re.search(r'Linker script and memory map', content)
        if not linker_script_match:
            return
            
        linker_content = content[linker_script_match.end():]
        
        # 查找所有形如 ^.段名 地址 大小 的行
        section_pattern = r'^(\.[a-zA-Z_][^\s]*)\s+0x([0-9a-f]+)\s+0x([0-9a-f]+)'
        matches = re.finditer(section_pattern, linker_content, re.MULTILINE)
        
        for match in matches:
            section_name = match.group(1)
            address = int(match.group(2), 16)
            size = int(match.group(3), 16)
            
            # 使用统一的段类型分类器，确保一致性
            classified_type = self._classify_section_type(section_name)

            if classified_type == 'text':
                section_type = 'PROGBITS'
                category = 'text'
            elif classified_type == 'data':
                section_type = 'PROGBITS'
                category = 'data'
            elif classified_type == 'bss':
                section_type = 'NOBITS'
                category = 'bss'
            elif classified_type == 'skip':
                # 跳过调试段等，不计入统计
                continue
            else:
                section_type = 'PROGBITS'
                category = 'other'
            
            self.sections[section_name] = {
                'address': address,
                'size': size,
                'type': section_type,
                'category': category
            }
        
        # 计算汇总大小（模拟size工具的计算方式）
        self.text_total = sum(info['size'] for info in self.sections.values() 
                             if info.get('category') == 'text')
        self.data_total = sum(info['size'] for info in self.sections.values() 
                             if info.get('category') == 'data')
        self.bss_total = sum(info['size'] for info in self.sections.values() 
                            if info.get('category') == 'bss')
            
    def _parse_symbols(self, content):
        """解析符号信息"""
        # 在Linker script and memory map之后查找符号
        linker_script_match = re.search(r'Linker script and memory map', content)
        if not linker_script_match:
            return
            
        # 从linker script部分开始解析
        linker_content = content[linker_script_match.end():]
        
        # 更全面的符号匹配模式
        # 匹配 .text.symbol_name 格式的符号
        text_symbol_pattern = r'^\s*\.text\.([^\s]+)\s+0x([0-9a-f]+)\s+0x([0-9a-f]+)\s+([^\n]+)'
        matches = re.finditer(text_symbol_pattern, linker_content, re.MULTILINE)
        
        for match in matches:
            symbol_name = match.group(1)
            address = int(match.group(2), 16)
            size = int(match.group(3), 16)
            file_info = match.group(4).strip()
            
            self.symbols[symbol_name] = {
                'address': address,
                'size': size,
                'type': 'T',  # Text section
                'file': file_info
            }
            
        # 匹配直接的符号定义，格式如:
        # 0x000000000c8ff338 symbol_name
        direct_symbol_pattern = r'^\s*0x([0-9a-f]+)\s+([a-zA-Z_][a-zA-Z0-9_:\.]+)\s*$'
        matches = re.finditer(direct_symbol_pattern, linker_content, re.MULTILINE)
        
        for match in matches:
            address = int(match.group(1), 16)
            symbol_name = match.group(2)
            
            # 避免重复添加已存在的符号
            if symbol_name not in self.symbols:
                self.symbols[symbol_name] = {
                    'address': address,
                    'size': 0,
                    'type': 'T',
                    'file': 'unknown'
                }
                
        # 优化的符号匹配 - 避免灾难性回溯
        # 使用更简单的正则表达式，避免复杂的嵌套匹配
        signature_pattern = r'^\s*0x([0-9a-f]+)\s+([a-zA-Z_][^\s\n]*)\s*$'
        matches = re.finditer(signature_pattern, linker_content, re.MULTILINE)

        for match in matches:
            address = int(match.group(1), 16)
            symbol_name = match.group(2)

            # 简单清理符号名
            clean_name = symbol_name.strip()

            if clean_name not in self.symbols:
                self.symbols[clean_name] = {
                    'address': address,
                    'size': 0,
                    'type': 'T',
                    'file': 'unknown'
                }
                    
    def _calculate_sizes(self):
        """计算各段大小 - 基于模块统计数据"""
        # 重新计算各段总大小
        self.text_size = 0
        self.data_size = 0
        self.bss_size = 0
        
        # 从sections直接计算，使用改进的分类器
        for section_name, section_info in self.sections.items():
            section_type = self._classify_section_type(section_name)
            section_size = section_info['size']
            
            if section_type == 'text':
                self.text_size += section_size
            elif section_type == 'data':
                self.data_size += section_size
            elif section_type == 'bss':
                self.bss_size += section_size
            # skip和other类型的段不计入主要分类
        
    def search_symbol(self, symbol_name, use_regex=False):
        """搜索符号"""
        results = []
        
        if use_regex:
            try:
                pattern = re.compile(symbol_name, re.IGNORECASE)
                for name, info in self.symbols.items():
                    if pattern.search(name):
                        results.append({
                            'name': name,
                            'address': f"{info['address']:08x}",
                            'size': f"{info['size']:x}" if info['size'] > 0 else "",
                            'type': info['type'],
                            'file': info.get('file', 'unknown')
                        })
            except re.error:
                return []
        else:
            for name, info in self.symbols.items():
                if symbol_name.lower() in name.lower():
                    results.append({
                        'name': name,
                        'address': f"{info['address']:08x}",
                        'size': f"{info['size']:x}" if info['size'] > 0 else "",
                        'type': info['type'],
                        'file': info.get('file', 'unknown')
                    })
                    
        return results
        
    def search_address(self, address):
        """通过地址搜索符号"""
        try:
            target_addr = int(address, 16)
        except ValueError:
            return None, None
            
        closest_symbol = None
        next_symbol = None
        
        # 按地址排序所有符号
        sorted_symbols = sorted(self.symbols.items(), key=lambda x: x[1]['address'])
        
        for name, info in sorted_symbols:
            if info['address'] <= target_addr:
                closest_symbol = (name, info)
            elif info['address'] > target_addr:
                next_symbol = (name, info)
                break
                
        return closest_symbol, next_symbol
        
    def get_sections_info(self):
        """获取节区信息，模拟readelf -S的输出格式"""
        sections_info = []
        
        # 添加标题
        sections_info.append("Section Headers:")
        sections_info.append("")
        
        # 表头
        title_format = (
            f"  {'[Nr]':<6} "
            f"{'Type':<12} "
            f"{'Address':<16} "
            f"{'Size':<12} "
            f"{'Name'}"
        )
        sections_info.append(title_format)
        sections_info.append('-' * 80)
        
        # 节区信息
        nr = 0
        for section_name, section_info in self.sections.items():
            nr += 1
            formatted_line = (
                f"  [{nr:>2}] "
                f"{section_info.get('type', 'PROGBITS'):<12} "
                f"0x{section_info['address']:08x}    "
                f"0x{section_info['size']:08x} "
                f"{section_name}"
            )
            sections_info.append(formatted_line)
            
        return '\n'.join(sections_info)
        
    def get_memory_usage(self):
        """
        获取内存使用信息 - 统一使用段统计数据
        这样可以确保与内存可视化图的一致性
        """
        other_size = self._calculate_other_size()
        return self.text_size, self.data_size, self.bss_size, other_size
    
    def get_module_memory_usage(self):
        """
        获取基于模块统计的内存使用信息 - 专门用于模块分析
        为了确保与内存占用信息的一致性，现在直接使用主段统计的结果
        这样可以避免子段统计的不完整性问题
        """
        # 直接使用主段统计的结果，确保一致性
        # 这样可以避免子段统计遗漏主段内容的问题
        return self.get_memory_usage()
    
    def _calculate_other_size(self):
        """计算其他类型段的总大小"""
        other_size = 0
        for section_name, section_info in self.sections.items():
            section_type = self._classify_section_type(section_name)
            if section_type == 'other':
                other_size += section_info['size']
        return other_size
        
    def create_memory_bar(self, text, data, bss, other=0):
        """
        创建内存占用可视化条
        
        注意: 这个可视化图基于ELF段统计，确保了与实际内存布局的一致性
        """
        total = text + data + bss + other
        if total == 0:
            return "无法创建内存占用图"

        width = 50
        text_width = int(width * text / total)
        data_width = int(width * data / total)
        bss_width = int(width * bss / total)
        other_width = width - text_width - data_width - bss_width

        bar = "\n 内存占用可视化：\n\n"
        bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
            "text:", "█" * text_width, text, text/total*100, width=width)
        bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
            "data:", "█" * data_width, data, data/total*100, width=width)
        bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
            "bss:", "█" * bss_width, bss, bss/total*100, width=width)
        if other > 0:
            bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
                "other:", "█" * other_width, other, other/total*100, width=width)
        bar += "总计: {:,} bytes".format(total)
        return bar
