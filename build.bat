@echo off
echo Building Compiler-Info-Eye...
echo.

REM 激活虚拟环境并编译
.venv\Scripts\python.exe -m PyInstaller --noconfirm cie.spec

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ===================================
    echo Build completed successfully!
    echo Output location: dist\cie\cie.exe
    echo ===================================
    pause
) else (
    echo.
    echo ===================================
    echo Build failed!
    echo ===================================
    pause
)
