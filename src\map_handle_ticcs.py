import re
import os
from pathlib import Path


class TiCcsMapFileParser:
    """Ti CCS MAP文件解析器"""

    def __init__(self, map_file_path):
        self.map_file_path = Path(map_file_path)
        self.sections = {}
        self.symbols = {}
        self.memory_config = {}
        self.memory_usage = {}
        self.module_usage = {}
        self.text_size = 0
        self.data_size = 0
        self.bss_size = 0
        self.segment_allocation = {}

    def parse_map_file(self):
        """解析Ti CCS MAP文件"""
        if not self.map_file_path.exists():
            raise FileNotFoundError(f"MAP文件不存在: {self.map_file_path}")

        with open(self.map_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        self._parse_memory_configuration(content)
        self._parse_segment_allocation_map(content)
        self._parse_section_allocation_map(content)
        self._parse_global_symbols(content)
        self._calculate_sizes()
        self._calculate_memory_usage(content)
        self._calculate_module_usage()

    def _parse_memory_configuration(self, content):
        """解析MEMORY CONFIGURATION部分"""
        # 查找MEMORY CONFIGURATION部分
        mem_config_match = re.search(r'MEMORY CONFIGURATION\s*\n.*?name\s+origin\s+length\s+used\s+unused\s+attr\s+fill\s*\n.*?-+.*?\n(.*?)(?=\n\s*\nSEGMENT ALLOCATION MAP)', content, re.DOTALL)

        if not mem_config_match:
            return

        mem_config_text = mem_config_match.group(1)

        # 解析每个memory段
        # 格式: name origin length used unused attr fill
        for line in mem_config_text.split('\n'):
            line = line.strip()
            if not line:
                continue

            # 匹配内存配置行，处理可能的空格对齐
            parts = line.split()
            if len(parts) >= 5:  # 至少需要name, origin, length, used, unused
                name = parts[0]
                try:
                    origin = int(parts[1], 16)
                    length = int(parts[2], 16)
                    used = int(parts[3], 16)
                    unused = int(parts[4], 16)
                    attributes = parts[5] if len(parts) > 5 else ""

                    self.memory_config[name] = {
                        'origin': origin,
                        'length': length,
                        'used': used,
                        'unused': unused,
                        'attributes': attributes,
                        'end': origin + length
                    }
                except ValueError:
                    continue

    def _parse_segment_allocation_map(self, content):
        """解析SEGMENT ALLOCATION MAP部分"""
        # 查找SEGMENT ALLOCATION MAP部分
        segment_match = re.search(r'SEGMENT ALLOCATION MAP\s*\n.*?\n.*?\n(.*?)(?=\n\s*\nSECTION ALLOCATION MAP)', content, re.DOTALL)

        if not segment_match:
            return

        segment_text = segment_match.group(1)

        # 解析段分配信息
        # 格式: run origin  load origin   length   init length attrs members
        for line in segment_text.split('\n'):
            line = line.strip()
            if not line or line.startswith('-'):
                continue

            # 匹配段信息行
            parts = line.split()
            if len(parts) >= 5:
                try:
                    run_origin = int(parts[0], 16)
                    load_origin = int(parts[1], 16)
                    length = int(parts[2], 16)
                    init_length = int(parts[3], 16)
                    attrs = parts[4] if len(parts) > 4 else ""

                    segment_key = f"{run_origin:08x}"
                    self.segment_allocation[segment_key] = {
                        'run_origin': run_origin,
                        'load_origin': load_origin,
                        'length': length,
                        'init_length': init_length,
                        'attributes': attrs
                    }
                except ValueError:
                    continue

    def _parse_section_allocation_map(self, content):
        """解析SECTION ALLOCATION MAP部分"""
        # 查找SECTION ALLOCATION MAP部分
        section_match = re.search(r'SECTION ALLOCATION MAP\s*\n.*?\n.*?\n.*?\n(.*?)(?=\n\s*\nGLOBAL SYMBOLS)', content, re.DOTALL)

        if not section_match:
            return

        section_text = section_match.group(1)

        # 解析段信息
        current_section = None
        for line in section_text.split('\n'):
            line_stripped = line.strip()
            if not line_stripped:
                continue

            # 检查是否是新的段定义
            if not line.startswith(' ') and not line.startswith('\t'):
                # 新段开始
                section_match = re.match(r'^(\.[^\s]+)\s+(\d+)\s+([0-9a-f]+)\s+([0-9a-f]+)', line_stripped)
                if section_match:
                    section_name = section_match.group(1)
                    page = int(section_match.group(2))
                    address = int(section_match.group(3), 16)
                    size = int(section_match.group(4), 16)

                    current_section = section_name

                    # 使用统一的段类型分类器
                    classified_type = self._classify_section_type(section_name)

                    if classified_type == 'text':
                        section_type = 'PROGBITS'
                        category = 'text'
                    elif classified_type == 'data':
                        section_type = 'PROGBITS'
                        category = 'data'
                    elif classified_type == 'bss':
                        section_type = 'NOBITS'
                        category = 'bss'
                    elif classified_type == 'skip':
                        continue
                    else:
                        section_type = 'PROGBITS'
                        category = 'other'

                    self.sections[section_name] = {
                        'address': address,
                        'size': size,
                        'type': section_type,
                        'category': category,
                        'page': page
                    }
            else:
                # 段内的详细信息，包含模块信息
                # Ti CCS格式: address size [library : file (.section)]
                detail_match = re.match(r'\s+([0-9a-f]+)\s+([0-9a-f]+)\s+(.+)', line_stripped)
                if detail_match and current_section:
                    try:
                        address = int(detail_match.group(1), 16)
                        size = int(detail_match.group(2), 16)
                        file_info = detail_match.group(3).strip()

                        if size == 0:
                            continue

                        # 解析模块名称
                        module_name = self._extract_module_name(file_info)
                        if module_name:
                            # 根据段名确定类型
                            section_type = self._classify_section_type(current_section)

                            # 跳过不需要统计的段
                            if section_type == 'skip':
                                continue

                            # 统计模块使用情况
                            if module_name not in self.module_usage:
                                self.module_usage[module_name] = {
                                    'text': 0, 'data': 0, 'bss': 0, 'total': 0
                                }

                            # 归类到text/data/bss
                            if section_type not in ['text', 'data', 'bss']:
                                section_type = 'data'

                            self.module_usage[module_name][section_type] += size
                            self.module_usage[module_name]['total'] += size
                    except ValueError:
                        # 如果地址或大小解析失败，跳过这一行
                        continue

    def _parse_global_symbols(self, content):
        """解析GLOBAL SYMBOLS部分"""
        # 查找GLOBAL SYMBOLS部分
        symbols_match = re.search(r'GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name\s*\n.*?\n.*?\n(.*?)$', content, re.DOTALL)

        if not symbols_match:
            return

        symbols_text = symbols_match.group(1)

        # 解析符号信息
        # 格式: address   name
        for line in symbols_text.split('\n'):
            line = line.strip()
            if not line:
                continue

            # 匹配符号行
            symbol_match = re.match(r'^([0-9a-f]+)\s+(.+)', line)
            if symbol_match:
                address = int(symbol_match.group(1), 16)
                symbol_name = symbol_match.group(2).strip()

                self.symbols[symbol_name] = {
                    'address': address,
                    'size': 0,  # Ti CCS map文件中符号没有大小信息
                    'type': 'T',  # 默认为Text类型
                    'file': 'unknown'
                }

    def _extract_module_name(self, file_info):
        """从文件信息中提取模块名称"""
        if not file_info:
            return None

        # 处理Ti CCS格式的文件信息
        # 格式可能是: library.a : file.obj (.section)
        # 或者: file.obj (.section)

        # 移除段信息 (.section)
        file_info = re.sub(r'\s*\([^)]+\)\s*$', '', file_info)

        # 检查是否包含库文件信息
        if ':' in file_info:
            parts = file_info.split(':')
            if len(parts) >= 2:
                lib_name = parts[0].strip()
                obj_name = parts[1].strip()

                # 如果有库名，优先使用库名
                if lib_name and not lib_name.startswith('--'):
                    return lib_name
                else:
                    return obj_name

        # 直接返回文件名
        return file_info.strip()

    def _classify_section_type(self, section_name):
        """分类段类型 - 兼容ARM GCC的分类逻辑"""
        if not section_name:
            return 'skip'

        section_lower = section_name.lower()

        # 调试段和其他不需要统计的段
        debug_patterns = [
            'debug', 'comment', 'note', 'stab', 'dwarf', 'line',
            'frame', 'eh_frame', 'gcc_except_table', 'jcr',
            'got', 'plt', 'rel', 'rela', 'hash', 'dynsym',
            'dynstr', 'interp', 'dynamic', 'shstrtab', 'symtab',
            'strtab', 'attributes', 'arm.exidx', 'arm.extab'
        ]

        for pattern in debug_patterns:
            if pattern in section_lower:
                return 'skip'

        # TEXT段（可执行代码段）
        if section_name.startswith('.text'):
            return 'text'

        # 检查含有text的段
        if 'text' in section_lower:
            return 'text'

        # 检查含有code的段
        code_patterns = ['_code', '.code', 'code_', 'code.']
        for pattern in code_patterns:
            if pattern in section_lower:
                return 'text'

        # BSS段（未初始化数据段）
        bss_patterns = [
            'bss', 'stack', 'heap', 'noinit', 'zero_init', 'common', 'sysmem'
        ]

        for pattern in bss_patterns:
            if (pattern == section_lower or
                f'.{pattern}' == section_lower or
                section_lower.startswith(f'{pattern}.') or
                section_lower.startswith(f'.{pattern}.') or
                section_lower.endswith(f'_{pattern}') or
                section_lower.endswith(f'.{pattern}') or
                pattern in section_lower):
                return 'bss'

        # DATA段（已初始化数据段）
        data_patterns = [
            'data', 'rodata', 'init_array', 'fini_array', 'ctors', 'dtors',
            'const', 'cinit', 'boardcfg_data'
        ]

        for pattern in data_patterns:
            if (pattern == section_lower or
                f'.{pattern}' == section_lower or
                section_lower.startswith(f'{pattern}.') or
                section_lower.startswith(f'.{pattern}.') or
                section_lower.endswith(f'_{pattern}') or
                section_lower.endswith(f'.{pattern}') or
                pattern in section_lower):
                return 'data'

        # 默认归类为data
        return 'data'

    def _calculate_sizes(self):
        """计算各段大小"""
        self.text_size = 0
        self.data_size = 0
        self.bss_size = 0

        for section_name, section_info in self.sections.items():
            size = section_info['size']
            category = section_info['category']

            if category == 'text':
                self.text_size += size
            elif category == 'data':
                self.data_size += size
            elif category == 'bss':
                self.bss_size += size

    def _calculate_memory_usage(self, content):
        """计算内存使用情况"""
        # Ti CCS已经在MEMORY CONFIGURATION中提供了used信息
        for name, config in self.memory_config.items():
            self.memory_usage[name] = {
                'used': config['used'],
                'total': config['length'],
                'free': config['unused'],
                'usage_percent': (config['used'] / config['length'] * 100) if config['length'] > 0 else 0
            }

    def _calculate_module_usage(self):
        """计算模块使用情况 - 已在_parse_section_allocation_map中完成"""
        pass

    def get_memory_usage(self):
        """获取内存使用信息"""
        return self.text_size, self.data_size, self.bss_size, 0

    def get_memory_usage_info(self):
        """获取详细的内存使用信息"""
        info = []
        total_text = self.text_size
        total_data = self.data_size
        total_bss = self.bss_size
        total_all = total_text + total_data + total_bss

        info.append(f"代码段 (TEXT): {total_text:,} bytes ({total_text/1024:.1f} KB)")
        info.append(f"数据段 (DATA): {total_data:,} bytes ({total_data/1024:.1f} KB)")
        info.append(f"BSS段 (BSS):   {total_bss:,} bytes ({total_bss/1024:.1f} KB)")
        info.append(f"总计:          {total_all:,} bytes ({total_all/1024:.1f} KB)")

        return '\n'.join(info)

    def create_memory_bar(self, text_size, data_size, bss_size, other_size):
        """创建内存使用条形图"""
        total = text_size + data_size + bss_size + other_size
        if total == 0:
            return "内存使用: [无数据]"

        bar_width = 50
        text_width = int((text_size / total) * bar_width)
        data_width = int((data_size / total) * bar_width)
        bss_width = int((bss_size / total) * bar_width)
        other_width = bar_width - text_width - data_width - bss_width

        bar = "内存使用: ["
        bar += "█" * text_width  # TEXT段
        bar += "▓" * data_width  # DATA段
        bar += "░" * bss_width   # BSS段
        bar += "·" * other_width # 其他
        bar += "]"

        legend = f"\n图例: █ TEXT({text_size/1024:.1f}KB) ▓ DATA({data_size/1024:.1f}KB) ░ BSS({bss_size/1024:.1f}KB)"
        if other_size > 0:
            legend += f" · 其他({other_size/1024:.1f}KB)"

        return bar + legend

    def get_module_usage_info(self):
        """获取模块使用信息"""
        if not self.module_usage:
            return "无模块使用信息"

        # 按总大小排序
        sorted_modules = sorted(self.module_usage.items(),
                              key=lambda x: x[1]['total'], reverse=True)

        info = []
        info.append("模块内存使用情况 (按总大小排序):")
        info.append("-" * 80)
        info.append(f"{'模块名':<30} {'TEXT':<10} {'DATA':<10} {'BSS':<10} {'总计':<10}")
        info.append("-" * 80)

        for module_name, usage in sorted_modules[:20]:  # 只显示前20个
            info.append(f"{module_name:<30} "
                       f"{usage['text']:<10} "
                       f"{usage['data']:<10} "
                       f"{usage['bss']:<10} "
                       f"{usage['total']:<10}")

        if len(sorted_modules) > 20:
            info.append(f"... 还有 {len(sorted_modules) - 20} 个模块")

        return '\n'.join(info)

    def search_symbol(self, symbol_name, use_regex=False):
        """搜索符号"""
        results = []

        if use_regex:
            try:
                pattern = re.compile(symbol_name, re.IGNORECASE)
                for name, info in self.symbols.items():
                    if pattern.search(name):
                        results.append({
                            'name': name,
                            'address': f"{info['address']:08x}",
                            'size': f"{info['size']:x}" if info['size'] > 0 else "",
                            'type': info['type'],
                            'file': info.get('file', 'unknown')
                        })
            except re.error:
                return []
        else:
            for name, info in self.symbols.items():
                if symbol_name.lower() in name.lower():
                    results.append({
                        'name': name,
                        'address': f"{info['address']:08x}",
                        'size': f"{info['size']:x}" if info['size'] > 0 else "",
                        'type': info['type'],
                        'file': info.get('file', 'unknown')
                    })

        return results

    def search_address(self, address):
        """通过地址搜索符号"""
        try:
            target_addr = int(address, 16)
        except ValueError:
            return None, None

        closest_symbol = None
        next_symbol = None

        # 按地址排序所有符号
        sorted_symbols = sorted(self.symbols.items(), key=lambda x: x[1]['address'])

        for name, info in sorted_symbols:
            if info['address'] <= target_addr:
                closest_symbol = (name, info)
            elif info['address'] > target_addr:
                next_symbol = (name, info)
                break

        return closest_symbol, next_symbol