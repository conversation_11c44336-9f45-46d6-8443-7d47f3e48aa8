import subprocess
import re
import os
from pathlib import Path


class ElfFileHandler:
    """ELF文件处理器"""
    
    def __init__(self, elf_file_path, tool_path):
        self.elf_file_path = Path(elf_file_path)
        self.tool_path = Path(tool_path)
        self.sections = {}
        self.symbols = {}
        self.text_size = 0
        self.data_size = 0
        self.bss_size = 0
        
    def analyze_elf_file(self):
        """分析ELF文件"""
        if not self.elf_file_path.exists():
            raise FileNotFoundError(f"ELF文件不存在: {self.elf_file_path}")
            
        if not self.tool_path.exists():
            raise FileNotFoundError(f"工具路径不存在: {self.tool_path}")
            
        self._get_size_info()
        self._get_sections_info()
        
    def _get_size_info(self):
        """获取ELF文件的尺寸信息"""
        size_exe = self.tool_path / 'arm-none-eabi-size.exe'
        
        try:
            result = subprocess.run(
                [str(size_exe), '-G', str(self.elf_file_path)],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                self.text_size, self.data_size, self.bss_size = self._parse_size_info(result.stdout)
            else:
                raise RuntimeError(f"size工具执行失败: {result.stderr}")
                
        except Exception as e:
            raise RuntimeError(f"获取尺寸信息失败: {str(e)}")
            
    def _parse_size_info(self, output):
        """解析size信息，返回text、data、bss的大小"""
        lines = output.split('\n')
        for line in lines:
            if 'text' in line and 'data' in line and 'bss' in line:
                # 跳过标题行
                continue
            parts = line.split()
            if len(parts) >= 4:  # 确保有足够的数据
                try:
                    text = int(parts[0])
                    data = int(parts[1])
                    bss = int(parts[2])
                    return text, data, bss
                except ValueError:
                    continue
        return 0, 0, 0
        
    def _get_sections_info(self):
        """获取ELF文件的节区信息"""
        readelf_exe = self.tool_path / 'arm-none-eabi-readelf.exe'
        
        try:
            result = subprocess.run(
                [str(readelf_exe), '-S', '--wide', str(self.elf_file_path)],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                self._parse_sections_info(result.stdout)
            else:
                raise RuntimeError(f"readelf工具执行失败: {result.stderr}")
                
        except Exception as e:
            raise RuntimeError(f"获取节区信息失败: {str(e)}")
            
    def _parse_sections_info(self, output):
        """解析节区信息"""
        lines = output.split('\n')
        
        for line in lines:
            if line.startswith('  ['):
                # 解析节区行，使用正则表达式
                match = re.match(r'\s*\[\s*(\d+)\]\s+([\.\w\-\_]+)\s+(\w+)\s+([0-9a-f]+)\s+([0-9a-f]+)\s+([0-9a-f]+)\s+([0-9a-f]+)\s+([A-Z]*)\s+(\d+)\s+(\d+)\s+(\d+)', line)
                if match:
                    nr, name, type_, addr, off, size, es, flg, lk, inf, al = match.groups()
                    
                    self.sections[name] = {
                        'nr': int(nr),
                        'type': type_,
                        'address': int(addr, 16),
                        'offset': int(off, 16),
                        'size': int(size, 16),
                        'entry_size': int(es, 16) if es else 0,
                        'flags': flg,
                        'link': int(lk),
                        'info': int(inf),
                        'align': int(al)
                    }
                    
    def search_symbol(self, symbol_name, use_regex=False):
        """搜索符号"""
        nm_exe = self.tool_path / 'arm-none-eabi-nm.exe'
        
        try:
            result = subprocess.run(
                [str(nm_exe), '--print-size', '--size-sort', '--radix=x', str(self.elf_file_path)],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                output_lines = result.stdout.split('\n')
                
                # 根据是否使用正则表达式选择不同的匹配方式
                if use_regex:
                    try:
                        pattern = re.compile(symbol_name, re.IGNORECASE)
                        matched_lines = [line for line in output_lines if pattern.search(line)]
                    except re.error:
                        return []
                else:
                    matched_lines = [line for line in output_lines if symbol_name.lower() in line.lower()]
                
                results = []
                for line in matched_lines:
                    if line.strip():  # 跳过空行
                        parts = line.split()
                        if len(parts) >= 2:
                            addr = parts[0]
                            size = parts[1] if len(parts) > 1 else ""
                            type_ = parts[2] if len(parts) > 2 else ""
                            name = " ".join(parts[3:]) if len(parts) > 3 else ""
                            
                            results.append({
                                'address': addr,
                                'size': size,
                                'type': type_,
                                'name': name,
                                'file': 'elf'
                            })
                            
                return results
            else:
                raise RuntimeError(f"nm工具执行失败: {result.stderr}")
                
        except Exception as e:
            raise RuntimeError(f"搜索符号失败: {str(e)}")
            
    def search_address(self, address):
        """通过地址搜索符号"""
        addr2line_exe = self.tool_path / 'arm-none-eabi-addr2line.exe'
        nm_exe = self.tool_path / 'arm-none-eabi-nm.exe'
        
        try:
            # 使用 addr2line 查找地址对应的符号
            addr2line_result = subprocess.run(
                [str(addr2line_exe), '-e', str(self.elf_file_path), '-f', '-a', address],
                capture_output=True,
                text=True
            )
            
            # 使用 nm 获取更多符号信息
            nm_result = subprocess.run(
                [str(nm_exe), '--numeric-sort', str(self.elf_file_path)],
                capture_output=True,
                text=True
            )
            
            addr2line_output = addr2line_result.stdout if addr2line_result.returncode == 0 else ""
            
            closest_symbol = None
            next_symbol = None
            
            if nm_result.returncode == 0:
                try:
                    target_addr = int(address, 16)
                    nm_lines = nm_result.stdout.split('\n')
                    
                    # 查找最接近但不超过目标地址的符号
                    for line in nm_lines:
                        if not line.strip():
                            continue
                        parts = line.split()
                        if len(parts) >= 3:
                            curr_addr = int(parts[0], 16)
                            if curr_addr <= target_addr:
                                closest_symbol = {
                                    'address': curr_addr,
                                    'type': parts[1],
                                    'name': parts[2] if len(parts) > 2 else "unknown",
                                    'offset': target_addr - curr_addr
                                }
                            elif curr_addr > target_addr:
                                next_symbol = {
                                    'address': curr_addr,
                                    'type': parts[1], 
                                    'name': parts[2] if len(parts) > 2 else "unknown"
                                }
                                break
                                
                except ValueError:
                    pass
                    
            return {
                'closest_symbol': closest_symbol,
                'next_symbol': next_symbol,
                'addr2line_output': addr2line_output
            }
            
        except Exception as e:
            raise RuntimeError(f"搜索地址失败: {str(e)}")
            
    def get_sections_info_formatted(self):
        """获取格式化的节区信息"""
        sections_info = []
        
        # 添加标题
        sections_info.append("Section Headers:")
        sections_info.append("")
        
        # 表头
        title_format = (
            f"  {'[Nr]':<6} "
            f"{'Type':<12} "
            f"{'Address':<16} "
            f"{'Size':<12} "
            f"{'ES':<3} "
            f"{'Flg':<4} "
            f"{'Lk':<3} "
            f"{'Inf':<3} "
            f"{'Al':<3} "
            f"{'Name'}"
        )
        sections_info.append(title_format)
        sections_info.append('-' * 100)
        
        # 节区信息
        for section_name, section_info in self.sections.items():
            formatted_line = (
                f"  [{section_info['nr']:>2}] "
                f"{section_info['type']:<12} "
                f"0x{section_info['address']:08x}    "
                f"0x{section_info['size']:08x} "
                f"{section_info['entry_size']:>3} "
                f"{section_info['flags']:<4} "
                f"{section_info['link']:>2} "
                f"{section_info['info']:>3} "
                f"{section_info['align']:>2} "
                f"{section_name}"
            )
            sections_info.append(formatted_line)
            
        return '\n'.join(sections_info)
        
    def get_memory_usage(self):
        """获取内存使用信息 - 与MAP处理器接口保持一致"""
        # ELF文件通常没有"other"类别，返回0
        return self.text_size, self.data_size, self.bss_size, 0
        
    def create_memory_bar(self, text, data, bss, other=0):
        """创建内存占用可视化条 - 与MAP处理器接口保持一致"""
        total = text + data + bss + other
        if total == 0:
            return "无法创建内存占用图"

        width = 50
        text_width = int(width * text / total)
        data_width = int(width * data / total)
        bss_width = int(width * bss / total)
        other_width = width - text_width - data_width - bss_width

        bar = "\n 内存占用可视化：\n\n"
        bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
            "text:", "█" * text_width, text, text/total*100, width=width)
        bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
            "data:", "█" * data_width, data, data/total*100, width=width)
        bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
            "bss:", "█" * bss_width, bss, bss/total*100, width=width)
        if other > 0:
            bar += "{:<8} {:<{width}} ({:,} bytes, {:.1f}%)\n\n".format(
                "other:", "█" * other_width, other, other/total*100, width=width)
        bar += "总计: {:,} bytes".format(total)
        return bar
