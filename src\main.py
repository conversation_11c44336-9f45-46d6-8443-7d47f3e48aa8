import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext, messagebox
import os
import subprocess
from pathlib import Path
import sys
import re
from map_handle import MapFileParser
from elf_handle import ElfFileHandler

class HistoryCombobox(ttk.Combobox):
    def __init__(self, parent, history_file=None, max_history=10, **kwargs):
        super().__init__(parent, **kwargs)
        self.max_history = max_history
        self.history = []
        self.history_file = history_file
        if self.history_file:
            self.load_history()

    def add_to_history(self, value):
        if not value or value in self.history:
            return
        self.history.insert(0, value)
        if len(self.history) > self.max_history:
            self.history.pop()
        self['values'] = self.history
        if self.history_file:
            self.save_history()

    def load_history(self):
        """从文件加载历史记录"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.history = [line.strip() for line in f.readlines()]
                    if self.history:
                        self.set(self.history[0])  # 设置最近的一条记录
                    self['values'] = self.history
        except Exception as e:
            print(f"加载历史记录失败: {str(e)}")

    def save_history(self):
        """保存历史记录到文件"""
        if not self.history_file:
            return
        try:
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
            with open(self.history_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.history))
        except Exception as e:
            print(f"保存历史记录失败: {str(e)}")

class SymbolWindow:
    def __init__(self, parent, symbol_name, tool_path, file_path, use_regex=False):
        self.window = tk.Toplevel(parent)
        self.window.title(f"Symbol: {symbol_name}")
        self.window.geometry("800x600")

        # 创建文本框
        self.text = scrolledtext.ScrolledText(
            self.window,
            width=700,
            height=500,
            font=('Consolas', 10),
            bg='white',
            fg='black'
        )
        self.text.pack(padx=10, pady=10)

        # 检查文件类型
        if file_path.lower().endswith('.map'):
            self._search_symbol_in_map(symbol_name, file_path, use_regex)
        else:
            self._search_symbol_in_elf(symbol_name, tool_path, file_path, use_regex)

    def _search_symbol_in_map(self, symbol_name, map_path, use_regex):
        """在MAP文件中搜索符号"""
        try:
            parser = MapFileParser(map_path)
            parser.parse_map_file()
            results = parser.search_symbol(symbol_name, use_regex)
            
            search_type = "正则表达式" if use_regex else "普通"
            
            if results:
                self.text.insert(tk.END, f"========= {search_type}搜索 '{symbol_name}' 的结果 (MAP文件) =========\n\n")
                
                # 添加标题行
                title = f"{'Address':<12} {'Size':<10} {'Type':<8} {'Name':<50} {'File'}\n"
                self.text.insert(tk.END, title)
                self.text.insert(tk.END, "-" * 80 + "\n")
                
                # 显示搜索结果
                for result in results:
                    formatted_line = f"{result['address']:<12} {result['size']:<10} {result['type']:<8} {result['name']:<50} {result['file'][:60]}\n"
                    self.text.insert(tk.END, formatted_line)
            else:
                self.text.insert(tk.END, f"未找到符号 '{symbol_name}' 的相关信息")
                
        except Exception as e:
            self.text.insert(tk.END, f"搜索符号时发生错误：{str(e)}")

    def _search_symbol_in_elf(self, symbol_name, tool_path, elf_path, use_regex):
        """在ELF文件中搜索符号"""
        try:
            elf_handler = ElfFileHandler(elf_path, tool_path)
            results = elf_handler.search_symbol(symbol_name, use_regex)
            
            search_type = "正则表达式" if use_regex else "普通"
            
            if results:
                self.text.insert(tk.END, f"========= {search_type}搜索 '{symbol_name}' 的结果 (ELF文件) =========\n\n")
                
                # 添加标题行
                title = f"{'Address':<12} {'Size':<10} {'Type':<8} {'Name':<50}\n"
                self.text.insert(tk.END, title)
                self.text.insert(tk.END, "-" * 80 + "\n")
                
                # 显示搜索结果
                for result in results:
                    formatted_line = f"{result['address']:<12} {result['size']:<10} {result['type']:<8} {result['name']}\n"
                    self.text.insert(tk.END, formatted_line)
            else:
                self.text.insert(tk.END, f"未找到符号 '{symbol_name}' 的相关信息")
                
        except Exception as e:
            self.text.insert(tk.END, f"搜索符号时发生错误：{str(e)}")


class AddrSearchWindow:
    def __init__(self, parent, addr, tool_path, file_path):
        self.window = tk.Toplevel(parent)
        self.window.title(f"Address: {addr}")
        self.window.geometry("800x600")

        # 创建文本框
        self.text = scrolledtext.ScrolledText(
            self.window,
            width=700,
            height=500,
            font=('Consolas', 10),
            bg='white',
            fg='black'
        )
        self.text.pack(padx=10, pady=10)

        # 检查文件类型
        if file_path.lower().endswith('.map'):
            self._search_address_in_map(addr, file_path)
        else:
            self._search_address_in_elf(addr, tool_path, file_path)
            
    def _search_address_in_map(self, addr, map_path):
        """在MAP文件中搜索地址"""
        try:
            parser = MapFileParser(map_path)
            parser.parse_map_file()
            closest_symbol, next_symbol = parser.search_address(addr)
            
            self.text.insert(tk.END, f"========= 地址 '{addr}' 的搜索结果 (MAP文件) =========\n\n")
            
            if closest_symbol:
                name, info = closest_symbol
                try:
                    target_addr = int(addr, 16)
                    offset = target_addr - info['address']
                    
                    self.text.insert(tk.END, "最近的符号：\n")
                    self.text.insert(tk.END, f"Symbol: {name}\n")
                    self.text.insert(tk.END, f"Base Address: 0x{info['address']:08x}\n")
                    self.text.insert(tk.END, f"Offset: +0x{offset:x}\n")
                    if info.get('file'):
                        self.text.insert(tk.END, f"File: {info['file']}\n")
                    self.text.insert(tk.END, "\n")
                except ValueError:
                    self.text.insert(tk.END, "地址格式无效\n\n")
                    
            if next_symbol:
                name, info = next_symbol
                self.text.insert(tk.END, "下一个符号：\n")
                self.text.insert(tk.END, f"Symbol: {name}\n")
                self.text.insert(tk.END, f"Address: 0x{info['address']:08x}\n")
                if info.get('file'):
                    self.text.insert(tk.END, f"File: {info['file']}\n")
                self.text.insert(tk.END, "\n")
                
            if not closest_symbol and not next_symbol:
                self.text.insert(tk.END, f"未找到地址 '{addr}' 附近的符号信息")
                
        except Exception as e:
            self.text.insert(tk.END, f"搜索地址时发生错误：{str(e)}")

    def _search_address_in_elf(self, addr, tool_path, elf_path):
        """在ELF文件中搜索地址"""
        try:
            elf_handler = ElfFileHandler(elf_path, tool_path)
            search_result = elf_handler.search_address(addr)
            
            self.text.insert(tk.END, f"========= 地址 '{addr}' 的搜索结果 (ELF文件) =========\n\n")
            
            if search_result['closest_symbol']:
                closest = search_result['closest_symbol']
                self.text.insert(tk.END, "最近的符号：\n")
                self.text.insert(tk.END, f"Symbol: {closest['name']}\n")
                self.text.insert(tk.END, f"Base Address: 0x{closest['address']:08x}\n")
                self.text.insert(tk.END, f"Offset: +0x{closest['offset']:x}\n\n")
                
            if search_result['next_symbol']:
                next_sym = search_result['next_symbol']
                self.text.insert(tk.END, "下一个符号：\n")
                self.text.insert(tk.END, f"Symbol: {next_sym['name']}\n")
                self.text.insert(tk.END, f"Address: 0x{next_sym['address']:08x}\n\n")
                
            if search_result['addr2line_output']:
                self.text.insert(tk.END, "addr2line 结果：\n")
                for line in search_result['addr2line_output'].split('\n'):
                    if line.strip():
                        self.text.insert(tk.END, f"{line}\n")
            
            if not search_result['closest_symbol'] and not search_result['next_symbol']:
                self.text.insert(tk.END, f"未找到地址 '{addr}' 附近的符号信息")
                
        except Exception as e:
            self.text.insert(tk.END, f"搜索地址时发生错误：{str(e)}")

class CompilerInfoEye:
    def __init__(self, root):
        self.root = root
        self.root.title("Compiler-info-eye v1.3")
        self.root.geometry("1000x600")

        # 获取程序运行路径
        if getattr(sys, 'frozen', False):
            self.app_path = Path(sys._MEIPASS)
        else:
            self.app_path = Path(__file__).parent.parent

        # 确保历史记录目录存在
        history_dir = self.app_path / 'history'
        os.makedirs(history_dir, exist_ok=True)

        # Frame A - Tool selection and ELF file selection
        frame_a = ttk.Frame(root, padding="10")
        frame_a.pack(fill=tk.X)

        # Tool selection
        ttk.Label(frame_a, text="arm-gcc工具链:").pack(side=tk.LEFT, padx=5)
        self.tool_var = tk.StringVar()
        self.tool_combo = ttk.Combobox(frame_a, textvariable=self.tool_var, state='readonly', width=30)
        self.update_tool_list()
        self.tool_combo.pack(side=tk.LEFT, padx=5)

        # ELF/MAP file selection with history
        ttk.Label(frame_a, text="ELF/MAP:").pack(side=tk.LEFT, padx=5)
        self.elf_combo = HistoryCombobox(
            frame_a,
            history_file=str(history_dir / 'elf_history.txt'),
            width=50
        )
        self.elf_combo.pack(side=tk.LEFT, padx=5)

        ttk.Button(frame_a, text="选择文件", command=self.browse_elf).pack(side=tk.LEFT, padx=5)
        ttk.Button(frame_a, text="解析", command=self.analyze_elf).pack(side=tk.LEFT, padx=5)

        # Symbol search with history
        frame_a2 = ttk.Frame(root, padding="10")
        frame_a2.pack(fill=tk.X)

        ttk.Label(frame_a2, text="Symbol搜索:").pack(side=tk.LEFT, padx=5)
        self.symbol_combo = HistoryCombobox(
            frame_a2,
            history_file=str(history_dir / 'symbol_history.txt'),
            width=50
        )
        self.symbol_combo.pack(side=tk.LEFT, padx=5)

        # 添加正则表达式复选框
        self.use_regex_var = tk.BooleanVar()
        self.use_regex_var.set(True)
        regex_check = ttk.Checkbutton(
            frame_a2,
            text="正则",
            variable=self.use_regex_var
        )
        regex_check.pack(side=tk.LEFT, padx=5)

        # 添加正则表达式帮助按钮
        help_button = ttk.Button(
            frame_a2,
            text="?",
            width=2,
            command=self.show_regex_help
        )
        help_button.pack(side=tk.LEFT, padx=2)

        ttk.Button(frame_a2, text="搜索", command=self.search_symbol).pack(side=tk.LEFT, padx=5)

        # 添加地址搜索框和按钮
        ttk.Label(frame_a2, text="地址:").pack(side=tk.LEFT, padx=5)
        self.addr_entry = ttk.Entry(frame_a2, width=20)
        self.addr_entry.pack(side=tk.LEFT, padx=5)
        ttk.Button(frame_a2, text="查找符号", command=self.search_addr).pack(side=tk.LEFT, padx=5)

        # Frame B - Results display
        frame_b = ttk.Frame(root, padding="10")
        frame_b.pack(fill=tk.BOTH, expand=True)

        self.result_text = scrolledtext.ScrolledText(
            frame_b,
            width=1000,
            height=600,
            font=('Consolas', 10),
            bg='white',
            fg='black'
        )
        self.result_text.pack(fill=tk.BOTH, expand=True)

    def update_tool_list(self):
        bin_tools_dir = self.app_path / 'bin-tools'
        if not bin_tools_dir.exists():
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"错误: 找不到bin-tools目录: {bin_tools_dir}")
            return

        tools = [d.name for d in bin_tools_dir.iterdir() if d.is_dir() and d.name.startswith('gcc-arm')]
        self.tool_combo['values'] = tools
        if tools:
            self.tool_combo.set(tools[0])

    def browse_elf(self):
        filename = filedialog.askopenfilename(
            filetypes=[("ELF/MAP files", "*.elf;*.map"), ("ELF files", "*.elf"), ("MAP files", "*.map"), ("All files", "*.*")]
        )
        if filename:
            self.elf_combo.set(filename)
            self.elf_combo.add_to_history(filename)

    def show_regex_help(self):
        """显示正则表达式帮助信息"""
        help_text = """正则表达式搜索帮助：

1. 基本匹配：
   - . 匹配任意单个字符
   - * 匹配前面的字符0次或多次
   - + 匹配前面的字符1次或多次
   - ? 匹配前面的字符0次或1次

2. 常用示例：
   - ^init 匹配以init开头的符号
   - task$ 匹配以task结尾的符号
   - [Mm]ain 匹配Main或main
   - init.*task 匹配init和task之间有任意字符的符号

3. 特殊字符：
   如果要匹配特殊字符(如 . * + ? ^ $ [ ] ( ) { } | \)，
   需要在前面加反斜杠 \\ 进行转义。
        """
        messagebox.showinfo("正则表达式帮助", help_text)

    def search_symbol(self):
        symbol = self.symbol_combo.get().strip()
        if not symbol:
            return

        file_path = self.elf_combo.get()
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", "请先选择有效的ELF或MAP文件")
            return

        # 如果是MAP文件，不需要工具链
        if not file_path.lower().endswith('.map'):
            if not self.tool_var.get():
                messagebox.showerror("错误", "请先选择ELF解析工具")
                return

        # 获取是否使用正则表达式
        use_regex = self.use_regex_var.get()

        self.symbol_combo.add_to_history(symbol)
        tool_path = self.app_path / 'bin-tools' / self.tool_var.get() if self.tool_var.get() else None
        SymbolWindow(self.root, symbol, tool_path, file_path, use_regex)

    def search_addr(self):
        """通过地址搜索符号"""
        addr = self.addr_entry.get().strip()
        if not addr:
            messagebox.showerror("错误", "请输入要搜索的地址")
            return

        # 验证地址格式（应该是16进制）
        try:
            # 尝试将输入转换为16进制，检查格式
            int(addr, 16)
        except ValueError:
            messagebox.showerror("错误", "请输入有效的16进制地址（例如：08000000）")
            return

        file_path = self.elf_combo.get()
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", "请先选择有效的ELF或MAP文件")
            return

        # 如果是MAP文件，不需要工具链
        if not file_path.lower().endswith('.map'):
            if not self.tool_var.get():
                messagebox.showerror("错误", "请先选择ELF解析工具")
                return

        tool_path = self.app_path / 'bin-tools' / self.tool_var.get() if self.tool_var.get() else None
        AddrSearchWindow(self.root, addr, tool_path, file_path)

    def analyze_elf(self):
        file_path = self.elf_combo.get()

        if not file_path or not os.path.exists(file_path):
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "错误: 请选择有效的ELF或MAP文件")
            return

        self.result_text.delete(1.0, tk.END)

        # 检查文件类型
        if file_path.lower().endswith('.map'):
            self._analyze_map_file(file_path)
        else:
            self._analyze_elf_file(file_path)

    def _analyze_map_file(self, map_path):
        """分析MAP文件"""
        try:
            parser = MapFileParser(map_path)
            parser.parse_map_file()
            
            # 获取内存使用信息
            text, data, bss, other = parser.get_memory_usage()
            memory_config_info = parser.get_memory_usage_info()
            memory_bar = parser.create_memory_bar(text, data, bss, other)
            self.result_text.insert(tk.END, "M======================================= 内存占用信息 =======================================\n")
            self.result_text.insert(tk.END, memory_bar + "\n\n")
            self.result_text.insert(tk.END, memory_config_info + "\n\n")
            
            # 获取模块占用分析
            module_usage_info = parser.get_module_usage_info()
            self.result_text.insert(tk.END, "======================================= 模块占用分析 =======================================\n")
            self.result_text.insert(tk.END, module_usage_info + "\n\n")
            
            # 获取节区信息
            sections_info = parser.get_sections_info()
            self.result_text.insert(tk.END, "========================================= 节区信息 =======================================\n\n")
            self.result_text.insert(tk.END, sections_info)

            # 获取段分类调试信息（可选）
            debug_info = parser.get_section_classification_debug()
            self.result_text.insert(tk.END, "\n\n===================================== 段分类调试信息 =====================================\n")
            self.result_text.insert(tk.END, debug_info)
            
        except Exception as e:
            self.result_text.insert(tk.END, f"分析MAP文件失败: {str(e)}\n")

    def _analyze_elf_file(self, elf_path):
        """分析ELF文件"""
        if not self.tool_var.get():
            self.result_text.insert(tk.END, "错误: 请先选择ELF解析工具")
            return
            
        tool_path = self.app_path / 'bin-tools' / self.tool_var.get()
        
        try:
            elf_handler = ElfFileHandler(elf_path, tool_path)
            elf_handler.analyze_elf_file()
            
            # 获取内存使用信息
            text, data, bss, other = elf_handler.get_memory_usage()
            memory_bar = elf_handler.create_memory_bar(text, data, bss, other)
            self.result_text.insert(tk.END, "E======================================= 内存占用信息 =======================================\n")
            self.result_text.insert(tk.END, memory_bar + "\n\n")
            
            # 获取节区信息
            sections_info = elf_handler.get_sections_info_formatted()
            self.result_text.insert(tk.END, "========================================= 节区信息 =======================================\n\n")
            self.result_text.insert(tk.END, sections_info)
            
        except Exception as e:
            self.result_text.insert(tk.END, f"分析ELF文件失败: {str(e)}\n")

def main():
    root = tk.Tk()
    app = CompilerInfoEye(root)
    root.mainloop()

if __name__ == '__main__':
    main()